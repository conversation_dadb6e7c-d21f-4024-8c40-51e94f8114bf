import React, { useState, useEffect } from 'react'
import { Table, Input, Select, DatePicker, Form, Cascader } from 'antd'
import './index.less'

const { Option } = Select
const { RangePicker } = DatePicker

/**
 * GroupedTable - 一个可重用的表格组件，支持分组表头、查询条件和分页
 *
 * @param {Object} props
 * @param {Array} props.columns - 表头配置，支持分组
 * @param {Array} props.dataSource - 表格数据源
 * @param {Array} props.queryFields - 查询条件配置，可以通过width属性设置每个查询条件的宽度
 * @param {Function} props.onSearch - 搜索回调函数
 * @param {Object} props.pagination - 分页配置
 * @param {Boolean} props.loading - 加载状态
 * @param {Object} props.tableProps - 额外的表格属性
 * @param {Object} props.initialValues - 查询条件的初始值
 * @param {String} props.fieldAlign - 查询条件的对齐方式，默认为 'start'
 * @returns {JSX.Element}
 */
const GroupedTable = ({
  columns = [],
  dataSource = [],
  queryFields = [],
  onSearch,
  pagination = {},
  loading = false,
  tableProps = {},
  initialValues = {},
  fieldAlign = 'start',
}) => {
  const [form] = Form.useForm()
  const [tableData, setTableData] = useState(dataSource)
  const [tablePagination, setTablePagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `总条数: ${total}`,
    ...pagination,
  })

  useEffect(() => {
    setTableData(dataSource)
    if (pagination?.total !== undefined) {
      setTablePagination((prev) => ({
        ...prev,
        total: pagination.total,
        current: pagination.current || prev.current,
        pageSize: pagination.pageSize || prev.pageSize,
      }))
    }
  }, [dataSource, pagination])

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setTablePagination((prev) => ({
      ...prev,
      current: pagination.current,
      pageSize: pagination.pageSize,
    }))

    if (onSearch) {
      const values = form.getFieldsValue()
      onSearch({
        ...values,
        pageSize: pagination.pageSize,
        current: pagination.current,
        sorter,
        filters,
      })
    }
  }

  // 处理搜索按钮点击
  const handleSearch = () => {
    const values = form.getFieldsValue()
    setTablePagination((prev) => ({
      ...prev,
      current: 1,
    }))

    if (onSearch) {
      onSearch({
        ...values,
        pageSize: tablePagination.pageSize,
        current: 1,
      })
    }
  }

  // 根据类型渲染查询字段
  const renderQueryField = (field) => {
    const { type, name, label, options, props = {} } = field

    switch (type) {
      case 'input':
        return <Input placeholder={`请输入${label}`} allowClear {...props} />
      case 'select':
        return (
          <Select
            onChange={handleSearch}
            placeholder={`请选择${label}`}
            allowClear
            {...props}
          >
            {options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        )
      case 'datePicker':
        return <DatePicker placeholder={`请选择${label}`} {...props} />
      case 'rangePicker':
        return <RangePicker placeholder={['开始日期', '结束日期']} {...props} />
      case 'custom':
        return field.render ? field.render() : null
      case 'Cascader':
        return (
          <Cascader
            options={options}
            placeholder={`请选择${label}`}
            onChange={handleSearch}
            changeOnSelect
            allowClear
            {...props}
          />
        )
      default:
        return null
    }
  }

  return (
    <div>
      {queryFields.length > 0 && (
        <div className="mb-16 bg-white rounded-4">
          <Form
            form={form}
            initialValues={initialValues}
            layout="inline"
            className={`w-full flex justify-${fieldAlign}`}
          >
            {queryFields.map((field, index) => (
              <Form.Item
                key={index}
                label={field.label}
                name={field.name}
                style={{ width: field.width || '280px' }}
              >
                {renderQueryField(field)}
              </Form.Item>
            ))}
          </Form>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={tableData}
        pagination={{
          ...tablePagination,
          className: 'mt-16 text-right',
        }}
        onChange={handleTableChange}
        // loading={loading}
        bordered
        size="middle"
        scroll={{ x: 'max-content' }}
        className="[&_.business-ant-table-thead_.business-ant-table-cell]:bg-[#f7f8f9] [&_.business-ant-table-thead_.business-ant-table-cell]:font-500 [&_.business-ant-table-thead_.business-ant-table-cell]:text-[#5c626b] [&_.business-ant-table-row:hover_.business-ant-table-cell]:bg-[#f6f8fd] [&_.business-ant-pagination-item-link]:flex [&_.business-ant-pagination-item-link]:items-center [&_.business-ant-pagination-item-link]:justify-center [&_.business-ant-table-cell]:align-middle"
        {...tableProps}
      />
    </div>
  )
}

export default GroupedTable
